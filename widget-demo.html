<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Antier Policy Assistant - Widget Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .container {
            text-align: center;
            max-width: 800px;
            padding: 2rem;
        }

        .logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            margin: 0 auto 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin: 3rem 0;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .feature h3 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .feature p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .cta {
            background: rgba(255, 255, 255, 0.15);
            padding: 2rem;
            border-radius: 16px;
            margin-top: 3rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .cta h2 {
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .cta p {
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }

        .widget-indicator {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .footer {
            margin-top: 3rem;
            opacity: 0.7;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖</div>
        
        <h1>Antier Policy Assistant</h1>
        
        <p class="subtitle">
            Your intelligent companion for navigating organizational policies, procedures, and guidelines. 
            Get instant, accurate answers to all your policy-related questions.
        </p>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">💬</div>
                <h3>Smart Conversations</h3>
                <p>AI-powered responses that understand context and provide relevant policy information</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔒</div>
                <h3>Secure & Compliant</h3>
                <p>Built with enterprise-grade security and compliance standards in mind</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <h3>Instant Answers</h3>
                <p>Get immediate responses to policy questions without searching through documents</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📱</div>
                <h3>Always Available</h3>
                <p>Access policy assistance 24/7 from any device, anywhere</p>
            </div>
        </div>

        <div class="cta">
            <h2>Try the Assistant</h2>
            <p>Click the chat button in the bottom-right corner to start a conversation with our AI policy assistant.</p>
            <p>Ask questions like:</p>
            <ul style="text-align: left; max-width: 400px; margin: 1rem auto; opacity: 0.9;">
                <li>"What is the data protection policy?"</li>
                <li>"How do I report a security incident?"</li>
                <li>"What are the remote work guidelines?"</li>
                <li>"Tell me about compliance requirements"</li>
            </ul>
        </div>

        <div class="footer">
            <p>Powered by Antier Solutions | AI Policy Assistant v1.0</p>
        </div>
    </div>

    <div class="widget-indicator">
        👆 Try the chat widget!
    </div>

    <!-- Antier Widget Integration -->
    <script src="https://backend-service-production-3acf.up.railway.app/widget.js"></script>
    <script>
        AniterWidget.init({
            apiKey: '5gWtRAYp7PXnW6rXOVaxXCLPcJ8Pad1k',
            apiUrl: 'https://backend-service-production-3acf.up.railway.app',
            theme: 'light',
            position: 'bottom-right',
            title: 'Antier Policy Assistant'
        });
    </script>
</body>
</html>
