export interface StreamingMessage {
  type: "session" | "content" | "done" | "error";
  content?: string;
  sessionId?: string;
  timestamp?: string;
  timing?: { total: number };
  error?: string;
  errorType?: string;
}

export interface StreamingCallbacks {
  onMessage: (message: StreamingMessage) => void;
  onError: (error: string, errorType?: string) => void;
  onComplete: () => void;
  onRetry?: (attempt: number) => void;
  onSessionUpdate?: (sessionId: string) => void;
}

// Simple markdown parser for basic formatting
export const parseMarkdown = (text: string): string => {
  return (
    text
      // Bold text
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      // Italic text
      .replace(/\*(.*?)\*/g, "<em>$1</em>")
      // Code blocks
      .replace(/```([\s\S]*?)```/g, "<pre><code>$1</code></pre>")
      // Inline code
      .replace(/`([^`]+)`/g, "<code>$1</code>")
      // Headers
      .replace(/^### (.*$)/gm, "<h3>$1</h3>")
      .replace(/^## (.*$)/gm, "<h2>$1</h2>")
      .replace(/^# (.*$)/gm, "<h1>$1</h1>")
      // Lists
      .replace(/^\* (.*$)/gm, "<li>$1</li>")
      .replace(/^- (.*$)/gm, "<li>$1</li>")
      // Line breaks
      .replace(/\n/g, "<br>")
      // Email addresses
      .replace(
        /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g,
        '<a href="mailto:$1" class="email-link">$1</a>'
      )
  );
};

export class StreamingService {
  private abortController: AbortController | null = null;
  private retryCount = 0;
  private maxRetries = 3;
  private retryDelay = 1000;
  private currentSessionId: string | null = null;

  // Method to set session ID
  setSessionId(sessionId: string | null) {
    this.currentSessionId = sessionId;
  }

  // Method to get current session ID
  getSessionId(): string | null {
    return this.currentSessionId;
  }

  // Method to clear session
  clearSession() {
    this.currentSessionId = null;
  }

  async sendMessage(
    query: string,
    callbacks: StreamingCallbacks
  ): Promise<void> {
    this.cleanup();
    this.retryCount = 0;

    return this.attemptConnection(query, callbacks);
  }

  private async attemptConnection(
    query: string,
    callbacks: StreamingCallbacks
  ): Promise<void> {
    try {
      const url = import.meta.env.VITE_API_URL
        ? `${import.meta.env.VITE_API_URL}/ask`
        : `/ask`;

      console.log("🚀 Sending request to backend:", url);
      console.log("📝 Query:", query);
      console.log("🔗 Session ID:", this.currentSessionId);

      this.abortController = new AbortController();

      // Include sessionId in the request body if available
      const requestBody: { query: string; sessionId?: string } = { query };
      if (this.currentSessionId) {
        requestBody.sessionId = this.currentSessionId;
      }

      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "text/event-stream",
          "Cache-Control": "no-cache",
        },
        body: JSON.stringify(requestBody),
        signal: this.abortController.signal,
      });

      if (!response.ok) {
        const errorType = this.getErrorType(response.status);
        let errorMessage = `HTTP error! status: ${response.status}`;

        if (response.status === 429) {
          errorMessage =
            "Rate limit exceeded. Please wait before trying again.";
        } else if (response.status >= 500) {
          errorMessage =
            "Server is temporarily unavailable. Please try again later.";
        } else if (response.status === 401 || response.status === 403) {
          errorMessage = "Authentication failed. Please contact support.";
        }

        throw new Error(`${errorType}:${errorMessage}`);
      }

      if (!response.body) {
        throw new Error("Response body is null");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split("\n");

          for (const line of lines) {
            if (line.trim() && line.startsWith("data: ")) {
              const data = line.slice(6); // Remove 'data: ' prefix

              if (data.trim()) {
                try {
                  const parsedData = JSON.parse(data);

                  // Handle session information
                  if (
                    parsedData.sessionId &&
                    parsedData.sessionId !== this.currentSessionId
                  ) {
                    this.currentSessionId = parsedData.sessionId;
                    console.log(
                      "🔗 Session ID updated:",
                      this.currentSessionId
                    );
                    if (this.currentSessionId) {
                      callbacks.onSessionUpdate?.(this.currentSessionId);
                    }
                  }

                  // Parse markdown in content if present
                  if (parsedData.type === "content" && parsedData.content) {
                    parsedData.content = parseMarkdown(parsedData.content);
                  }

                  callbacks.onMessage(parsedData);

                  if (parsedData.type === "done") {
                    callbacks.onComplete();
                    this.cleanup();
                    return;
                  }
                } catch (error) {
                  console.error("Error parsing message:", error);
                  callbacks.onError(
                    "Failed to parse server response",
                    "server"
                  );
                }
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error("Connection error:", error);

      if (error instanceof Error && error.name === "AbortError") {
        // Request was aborted, don't retry
        return;
      }

      const { errorMessage, errorType } = this.parseError(error);

      if (this.retryCount < this.maxRetries && this.shouldRetry(errorType)) {
        this.retryCount++;
        callbacks.onRetry?.(this.retryCount);

        const delay = this.getRetryDelay(errorType, this.retryCount);
        setTimeout(() => {
          this.attemptConnection(query, callbacks);
        }, delay);
      } else {
        const finalMessage =
          this.retryCount >= this.maxRetries
            ? "Connection failed after multiple attempts. Please try again."
            : errorMessage;
        callbacks.onError(finalMessage, errorType);
        this.cleanup();
      }
    }
  }

  abort(): void {
    this.cleanup();
  }

  private cleanup(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  isConnected(): boolean {
    return (
      this.abortController !== null && !this.abortController.signal.aborted
    );
  }

  private getErrorType(status: number): string {
    if (status === 429) return "rate_limit";
    if (status >= 500) return "server";
    if (status === 401 || status === 403) return "api";
    if (status >= 400) return "api";
    return "unknown";
  }

  private parseError(error: unknown): {
    errorMessage: string;
    errorType: string;
  } {
    if (error instanceof Error) {
      if (error.name === "AbortError") {
        return { errorMessage: "Request was cancelled", errorType: "timeout" };
      }

      if (
        error.message.includes("Failed to fetch") ||
        error.message.includes("fetch")
      ) {
        return {
          errorMessage: "Network connection failed",
          errorType: "network",
        };
      }

      if (error.message.includes(":")) {
        const [type, message] = error.message.split(":", 2);
        return { errorMessage: message, errorType: type };
      }

      return { errorMessage: error.message, errorType: "unknown" };
    }

    return {
      errorMessage: "An unexpected error occurred",
      errorType: "unknown",
    };
  }

  private shouldRetry(errorType: string): boolean {
    // Don't retry authentication errors
    if (errorType === "api" && this.retryCount === 0) return false;
    // Retry network, server, and rate limit errors
    return ["network", "server", "rate_limit", "timeout", "unknown"].includes(
      errorType
    );
  }

  private getRetryDelay(errorType: string, attempt: number): number {
    switch (errorType) {
      case "rate_limit":
        return 2000 * attempt; // Longer delay for rate limits
      case "server":
        return 1500 * attempt; // Medium delay for server errors
      case "network":
        return 1000 * attempt; // Standard delay for network errors
      default:
        return this.retryDelay * attempt;
    }
  }
}
